import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Calendar, Users, Clock, ExternalLink, Trash2, ChevronDown, ChevronUp } from 'lucide-react';
import { PoolHistoryItem, poolHistoryUtils } from '@/utils/poolHistory';

interface PoolHistoryProps {
  onPoolSelect: (sessionId: string, creatorName: string) => void;
}

const PoolHistory = ({ onPoolSelect }: PoolHistoryProps) => {
  const [history, setHistory] = React.useState<PoolHistoryItem[]>([]);
  const [showAll, setShowAll] = React.useState(false);

  React.useEffect(() => {
    const loadHistory = () => {
      const poolHistory = poolHistoryUtils.getPoolHistory();
      setHistory(poolHistory);
    };

    loadHistory();

    // Listen for storage changes (in case user opens multiple tabs)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'whenworks_pool_history') {
        loadHistory();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  if (history.length === 0) {
    return null;
  }

  const handlePoolClick = (pool: PoolHistoryItem) => {
    // Update last visited time
    poolHistoryUtils.updateLastVisited(pool.sessionId);
    // Navigate to the pool
    onPoolSelect(pool.sessionId, pool.creatorName);
  };

  const handleRemovePool = (e: React.MouseEvent, sessionId: string) => {
    e.stopPropagation(); // Prevent pool click when clicking trash
    if (window.confirm('Remove this pool from your history?')) {
      poolHistoryUtils.removeFromHistory(sessionId);
      setHistory(prev => prev.filter(pool => pool.sessionId !== sessionId));
    }
  };

  const displayedPools = showAll ? history : history.slice(0, 2);
  const hasMorePools = history.length > 2;

  const renderPoolCard = (pool: PoolHistoryItem) => (
    <Card
      key={pool.sessionId}
      className="bg-white/90 backdrop-blur-sm border-gray-200 hover:border-green-300 hover:shadow-md transition-all duration-200 cursor-pointer relative"
      onClick={() => handlePoolClick(pool)}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-2">
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <Calendar className="w-4 h-4 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <h4 className="text-sm font-medium text-gray-900 truncate">
                  {pool.poolName}
                </h4>
                <p className="text-xs text-gray-500">
                  {pool.isCreator ? 'Created by you' : `Created by ${pool.creatorName}`}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4 text-xs text-gray-500">
              <div className="flex items-center space-x-1">
                <Users className="w-3 h-3" />
                <span>
                  {pool.participantCount ? `${pool.participantCount} participant${pool.participantCount !== 1 ? 's' : ''}` : 'No participants yet'}
                </span>
              </div>

              <div className="flex items-center space-x-1">
                <Clock className="w-3 h-3" />
                <span>{poolHistoryUtils.formatDate(pool.lastVisited)}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2 ml-3">
            {pool.isCreator && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700">
                Creator
              </span>
            )}
            <ExternalLink className="w-4 h-4 text-gray-400" />
          </div>
        </div>

        {/* Trash icon in bottom right */}
        <button
          onClick={(e) => handleRemovePool(e, pool.sessionId)}
          className="absolute bottom-2 right-2 p-1 text-gray-400 hover:text-red-500 transition-colors"
          title="Remove from history"
        >
          <Trash2 className="w-3 h-3" />
        </button>
      </CardContent>
    </Card>
  );

  return (
    <div className="mt-8 space-y-4">
      

      {/* Always show first 2 pools without scrolling */}
      <div className="space-y-3">
        {displayedPools.map(renderPoolCard)}
      </div>

      {/* Show all toggle for more than 2 pools */}
      {hasMorePools && (
        <div className="text-center">
          <button
            onClick={() => setShowAll(!showAll)}
            className="inline-flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900 transition-colors"
          >
            <span>{showAll ? 'Show less' : `Show all (${history.length})`}</span>
            {showAll ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </button>
        </div>
      )}

      {/* Scrollable list when showing all */}
      {showAll && hasMorePools && (
        <div className="space-y-3 max-h-80 overflow-y-auto">
          {history.slice(2).map(renderPoolCard)}
        </div>
      )}

      {history.length >= 5 && (
        <div className="text-center">
          <button
            onClick={() => {
              if (window.confirm('Are you sure you want to clear your pool history? This cannot be undone.')) {
                poolHistoryUtils.clearHistory();
                setHistory([]);
              }
            }}
            className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
          >
            Clear history
          </button>
        </div>
      )}
    </div>
  );
};

export default PoolHistory;
