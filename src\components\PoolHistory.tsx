import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Calendar, Users, Clock, ExternalLink } from 'lucide-react';
import { PoolHistoryItem, poolHistoryUtils } from '@/utils/poolHistory';

interface PoolHistoryProps {
  onPoolSelect: (sessionId: string, creatorName: string) => void;
}

const PoolHistory = ({ onPoolSelect }: PoolHistoryProps) => {
  const [history, setHistory] = React.useState<PoolHistoryItem[]>([]);

  React.useEffect(() => {
    const loadHistory = () => {
      const poolHistory = poolHistoryUtils.getPoolHistory();
      setHistory(poolHistory);
    };

    loadHistory();

    // Listen for storage changes (in case user opens multiple tabs)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'whenworks_pool_history') {
        loadHistory();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  if (history.length === 0) {
    return null;
  }

  const handlePoolClick = (pool: PoolHistoryItem) => {
    // Update last visited time
    poolHistoryUtils.updateLastVisited(pool.sessionId);
    // Navigate to the pool
    onPoolSelect(pool.sessionId, pool.creatorName);
  };

  return (
    <div className="mt-8 space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Your recent pools
        </h3>
        <p className="text-sm text-gray-600">
          Revisit pools you've created or joined before
        </p>
      </div>

      <div className="space-y-3 max-h-80 overflow-y-auto">
        {history.map((pool) => (
          <Card 
            key={pool.sessionId}
            className="bg-white/90 backdrop-blur-sm border-gray-200 hover:border-green-300 hover:shadow-md transition-all duration-200 cursor-pointer"
            onClick={() => handlePoolClick(pool)}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Calendar className="w-4 h-4 text-white" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {pool.poolName}
                      </h4>
                      <p className="text-xs text-gray-500">
                        {pool.isCreator ? 'Created by you' : `Created by ${pool.creatorName}`}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Users className="w-3 h-3" />
                      <span>
                        {pool.participantCount ? `${pool.participantCount} participant${pool.participantCount !== 1 ? 's' : ''}` : 'No participants yet'}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{poolHistoryUtils.formatDate(pool.lastVisited)}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 ml-3">
                  {pool.isCreator && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700">
                      Creator
                    </span>
                  )}
                  <ExternalLink className="w-4 h-4 text-gray-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {history.length >= 5 && (
        <div className="text-center">
          <button
            onClick={() => {
              if (window.confirm('Are you sure you want to clear your pool history? This cannot be undone.')) {
                poolHistoryUtils.clearHistory();
                setHistory([]);
              }
            }}
            className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
          >
            Clear history
          </button>
        </div>
      )}
    </div>
  );
};

export default PoolHistory;
