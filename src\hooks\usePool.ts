
import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface Pool {
  id: string;
  name: string;
  creator_name: string;
  session_id: string;
  created_at: string;
  updated_at: string;
}

interface Participant {
  id: string;
  pool_id: string;
  name: string;
  availability: string[];
  created_at: string;
  updated_at: string;
}

export const usePool = (sessionId: string) => {
  const [pool, setPool] = useState<Pool | null>(null);
  const [participants, setParticipants] = useState<Participant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch pool and participants
  const fetchPoolData = async () => {
    try {
      setLoading(true);

      // Fetch pool - use maybeSingle() to handle case where pool doesn't exist
      const { data: poolData, error: poolError } = await supabase
        .from('pools')
        .select('*')
        .eq('session_id', sessionId)
        .maybeSingle();

      if (poolError) {
        console.error('Pool fetch error:', poolError);
        setError('Failed to load pool');
        return;
      }

      // If no pool found, that's okay - it might need to be created
      if (!poolData) {
        setPool(null);
        setParticipants([]);
        setError(null);
        setLoading(false);
        return;
      }

      setPool(poolData);

      // Fetch participants
      const { data: participantsData, error: participantsError } = await supabase
        .from('participants')
        .select('*')
        .eq('pool_id', poolData.id)
        .order('created_at', { ascending: true });

      if (participantsError) {
        console.error('Participants fetch error:', participantsError);
        setError('Failed to load participants');
        return;
      }

      setParticipants(participantsData || []);
      setError(null);
    } catch (err) {
      console.error('Unexpected error:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Create pool
  const createPool = async (creatorName: string) => {
    try {
      const { data, error } = await supabase
        .from('pools')
        .insert({
          name: `${creatorName}'s Schedule`,
          creator_name: creatorName,
          session_id: sessionId
        })
        .select()
        .single();

      if (error) {
        console.error('Create pool error:', error);
        throw error;
      }

      setPool(data);

      // Add creator as first participant
      const creatorParticipant = await addParticipant(creatorName);

      return data;
    } catch (err) {
      console.error('Failed to create pool:', err);
      throw err;
    }
  };

  // Add participant
  const addParticipant = async (name: string) => {
    if (!pool) return null;

    try {
      const { data, error } = await supabase
        .from('participants')
        .insert({
          pool_id: pool.id,
          name: name.trim(),
          availability: []
        })
        .select()
        .single();

      if (error) {
        console.error('Add participant error:', error);
        throw error;
      }

      setParticipants(prev => [...prev, data]);
      return data;
    } catch (err) {
      console.error('Failed to add participant:', err);
      throw err;
    }
  };

  // Update participant availability
  const updateParticipantAvailability = async (participantId: string, availability: string[]) => {
    try {
      const { data, error } = await supabase
        .from('participants')
        .update({ availability })
        .eq('id', participantId)
        .select()
        .single();

      if (error) {
        console.error('Update availability error:', error);
        throw error;
      }

      setParticipants(prev =>
        prev.map(p => p.id === participantId ? { ...p, availability } : p)
      );

      return data;
    } catch (err) {
      console.error('Failed to update availability:', err);
      throw err;
    }
  };

  useEffect(() => {
    if (sessionId) {
      fetchPoolData();
    }
  }, [sessionId]);

  // Set up real-time subscriptions
  useEffect(() => {
    if (!pool) return;

    // Subscribe to participants changes
    const participantsSubscription = supabase
      .channel('participants-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'participants',
          filter: `pool_id=eq.${pool.id}`
        },
        () => {
          // Refetch participants when changes occur
          fetchPoolData();
        }
      )
      .subscribe();

    return () => {
      participantsSubscription.unsubscribe();
    };
  }, [pool?.id]);

  return {
    pool,
    participants,
    loading,
    error,
    createPool,
    addParticipant,
    updateParticipantAvailability,
    refetch: fetchPoolData
  };
};
