import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Calendar, Upload, MessageSquare, Lightbulb, Users, Briefcase, Heart } from 'lucide-react';
interface LandingPageProps {
  onCreateSession: (name: string) => void;
}
const LandingPage = ({
  onCreateSession
}: LandingPageProps) => {
  const [name, setName] = useState('');
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      onCreateSession(name.trim());
    }
  };
  return <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50">
      {/* Navigation */}
      <nav className="px-6 py-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-br from-green-600 to-emerald-700 rounded-lg flex items-center justify-center">
            <Calendar className="w-5 h-5 text-white" />
          </div>
          <span className="text-xl font-bold bg-gradient-to-r from-green-700 to-emerald-600 bg-clip-text text-transparent">
            Whenworks
          </span>
        </div>
        <div className="hidden md:flex items-center space-x-6">
          <a href="#how-it-works" className="text-gray-600 hover:text-gray-900 transition-colors">
            How it works
          </a>
          <a href="#who-its-for" className="text-gray-600 hover:text-gray-900 transition-colors">
            Who it's for
          </a>
          <Button variant="outline">Create your pool</Button>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="px-6 max-w-7xl mx-auto py-[119px]">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Hero text */}
          <div className="space-y-8">
            <div>
              <h1 className="text-5xl font-bold leading-tight mb-6 lg:text-7xl">
                <span className="text-gray-900">Stop the group chat chaos.</span>{' '}
                <span className="bg-gradient-to-r from-green-600 via-emerald-600 to-green-700 bg-clip-text text-transparent">Plan like a pro.</span>
              </h1>
              
              <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                Create a scheduling pool, share the link, and watch everyone drop their availability. 
                No accounts, no emails, no drama. Just pure coordination magic.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button onClick={() => {
              const nameInput = document.querySelector('input[placeholder="Enter your name"]') as HTMLInputElement;
              if (nameInput) {
                nameInput.focus();
              }
            }} className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 text-lg font-medium">Create your pool</Button>
              <Button variant="outline" className="px-8 py-3 text-lg font-medium">How it works</Button>
            </div>

            <div className="flex items-center space-x-6 text-sm text-gray-500">
              <span>✓ Zero setup required</span>
              <span>✓ No login nonsense</span>
              <span>✓ Works everywhere</span>
            </div>
          </div>

          {/* Right side - Name Input Card */}
          <div className="flex justify-center lg:justify-end">
            <Card className="w-full max-w-md bg-white/90 backdrop-blur-sm border-green-100 shadow-xl">
              <CardContent className="p-8">
                <div className="mb-6 text-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-600 to-emerald-700 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MessageSquare className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Create your scheduling pool
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Think of it as a shared calendar where everyone throws in their free time. 
                    Magic happens when schedules overlap.
                  </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <Input type="text" placeholder="Enter your name" value={name} onChange={e => setName(e.target.value)} className="text-center text-lg py-6 border-gray-200 focus:border-green-500 focus:ring-green-500" maxLength={50} required />
                  
                  <Button type="submit" className="w-full bg-green-600 hover:bg-green-700 text-white py-6 text-lg font-medium" disabled={!name.trim()}>Create pool &amp; get link</Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="px-6 py-20 bg-white/60">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6 text-gray-900">Dead simple scheduling</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Three steps to end the "when are you free?" nightmare forever
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="bg-white/80 backdrop-blur-sm border-green-100 hover:shadow-lg hover:scale-[1.02] transition-all duration-200">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Upload className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Drop your name, get a link
                </h3>
                <p className="text-gray-600 leading-relaxed mb-4">
                  No forms to fill out, no passwords to remember. Just type your name and boom - 
                  you've got a shareable scheduling pool ready to go.
                </p>
                <span className="inline-block bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium">
                  Step 1
                </span>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border-emerald-100 hover:shadow-lg hover:scale-[1.02] transition-all duration-200">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Calendar className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Everyone adds their availability
                </h3>
                <p className="text-gray-600 leading-relaxed mb-4">Share your link. Friends click, mark their free time, done. No app downloads, no account creation, no hassle.</p>
                <span className="inline-block bg-emerald-100 text-emerald-700 px-3 py-1 rounded-full text-sm font-medium">
                  Step 2
                </span>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border-green-100 hover:shadow-lg hover:scale-[1.02] transition-all duration-200">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-green-600 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Lightbulb className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  See the perfect time slots
                </h3>
                <p className="text-gray-600 leading-relaxed mb-4">
                  Watch the magic happen as overlapping availability lights up. 
                  Pick the time that works for the most people and you're done.
                </p>
                <span className="inline-block bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium">
                  Step 3
                </span>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Who It's For Section */}
      <section id="who-its-for" className="px-6 py-20">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6 text-gray-900">
              Built for real people planning real things
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Whether it's adventure or just hanging out, coordination shouldn't be the hardest part
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200 hover:shadow-lg hover:scale-[1.02] transition-all duration-200">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-green-600 to-green-700 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Heart className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Trip Planning Crews
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Planning that epic weekend getaway or month-long adventure? Stop the endless "what dates work for everyone?" 
                  and start the actual planning.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-200 hover:shadow-lg hover:scale-[1.02] transition-all duration-200">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-emerald-600 to-emerald-700 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Users className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Friend Groups
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Game nights, dinner parties, beach days, spontaneous hangouts. 
                  Whatever brings your crew together shouldn't require a PhD in logistics.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-50 to-emerald-100 border-green-200 hover:shadow-lg hover:scale-[1.02] transition-all duration-200">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-green-700 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <Calendar className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Community Organizers
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Running book clubs, volunteer events, hobby groups, or local meetups? 
                  Get everyone on the same page without the coordination headache.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="px-6 py-20 bg-gradient-to-br from-green-600 via-emerald-600 to-green-700">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to end scheduling chaos forever?
          </h2>
          <p className="text-xl text-green-100 mb-8 max-w-2xl mx-auto">
            Join thousands who've ditched the back-and-forth madness. 
            No apps to download, no accounts to create. Just pure scheduling magic.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button onClick={() => {
            const nameInput = document.querySelector('input[placeholder="Enter your name"]') as HTMLInputElement;
            if (nameInput) {
              nameInput.scrollIntoView({
                behavior: 'smooth'
              });
              nameInput.focus();
            }
          }} size="lg" className="bg-white text-green-600 hover:bg-gray-100 px-8 py-6 text-lg font-medium shadow-lg">Create your first pool</Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="px-6 py-8 bg-gray-50 border-t border-gray-200">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-gradient-to-br from-green-600 to-emerald-700 rounded-md flex items-center justify-center">
                <Calendar className="w-4 h-4 text-white" />
              </div>
              <span className="font-semibold text-gray-900">Whenworks</span>
            </div>
            <div className="text-center md:text-left">
              <p className="text-sm text-gray-600">
                No accounts. No emails. No hassle. Just pure coordination.
              </p>
            </div>
            <div className="text-sm text-gray-500">© 2025 Whenworks. All rights reserved.</div>
          </div>
        </div>
      </footer>
    </div>;
};
export default LandingPage;