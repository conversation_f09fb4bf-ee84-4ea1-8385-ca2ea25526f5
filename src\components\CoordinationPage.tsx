
import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Users, Calendar, ArrowLeft, UserPlus, Loader2, Check, Copy, AlertCircle } from 'lucide-react';
import CalendarGrid from './CalendarGrid';
import AvailabilityDisplay from './AvailabilityDisplay';
import { usePool } from '@/hooks/usePool';

interface CoordinationPageProps {
  sessionId: string;
  creatorName: string;
  onBack: () => void;
}

const CoordinationPage = ({ sessionId, creatorName, onBack }: CoordinationPageProps) => {
  const { pool, participants, loading, error, createPool, addParticipant, updateParticipantAvailability } = usePool(sessionId);
  const [newParticipantName, setNewParticipantName] = useState('');
  const [isAddingParticipant, setIsAddingParticipant] = useState(false);
  const [currentParticipant, setCurrentParticipant] = useState(0);
  const [shareUrl] = useState(`${window.location.origin}${window.location.pathname}${window.location.search}`);
  const [showJoinInterface, setShowJoinInterface] = useState(false);
  const [joinName, setJoinName] = useState('');
  const [isCreatingPool, setIsCreatingPool] = useState(false);
  const [hasJoined, setHasJoined] = useState(false);
  const [copyStatus, setCopyStatus] = useState<'idle' | 'copying' | 'copied' | 'error'>('idle');

  // Check if this is a shared URL visit (not the creator) or if pool doesn't exist
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const urlCreator = urlParams.get('creator');

    // If pool doesn't exist and we're the creator, create it
    if (!loading && !pool && !error && urlCreator === creatorName) {
      setIsCreatingPool(true);
      createPool(creatorName).finally(() => setIsCreatingPool(false));
    }
    // If pool exists and we're the creator, mark as joined and don't show join interface
    else if (!loading && pool && urlCreator === creatorName) {
      setHasJoined(true);
      setShowJoinInterface(false);
    }
    // If pool exists and someone else is visiting (different creator name), show join interface
    // Only show if they haven't joined yet
    else if (!loading && pool && !hasJoined && urlCreator && urlCreator !== creatorName) {
      setShowJoinInterface(true);
    }
    // If pool exists but no creator param in URL, this is a shared URL visit
    // Only show if they haven't joined yet
    else if (!loading && pool && !hasJoined && !urlCreator) {
      setShowJoinInterface(true);
    }
  }, [loading, pool, error, creatorName, createPool, hasJoined]);

  const handleDateToggle = async (dateStr: string) => {
    const participant = participants[currentParticipant];
    if (!participant) return;

    const newAvailability = participant.availability.includes(dateStr)
      ? participant.availability.filter(d => d !== dateStr)
      : [...participant.availability, dateStr];

    try {
      await updateParticipantAvailability(participant.id, newAvailability);
    } catch (err) {
      console.error('Failed to update availability:', err);
    }
  };

  // Ensure currentParticipant index is valid when participants change
  useEffect(() => {
    if (participants.length > 0 && (currentParticipant >= participants.length || currentParticipant < 0)) {
      setCurrentParticipant(0);
    }
  }, [participants, currentParticipant]);

  const handleAddParticipant = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newParticipantName.trim() || !pool) return;

    // Check if participant already exists
    const existingParticipant = participants.find(p =>
      p.name.toLowerCase() === newParticipantName.trim().toLowerCase()
    );

    if (existingParticipant) {
      setCurrentParticipant(participants.indexOf(existingParticipant));
    } else {
      try {
        const newParticipant = await addParticipant(newParticipantName.trim());
        if (newParticipant) {
          // Set current participant to the newly added participant
          // The participants array will be updated by the addParticipant function
          // so the new participant will be at index participants.length (before the update)
          setCurrentParticipant(participants.length);
        }
      } catch (err) {
        console.error('Failed to add participant:', err);
      }
    }

    setNewParticipantName('');
    setIsAddingParticipant(false);
  };

  const handleJoinPool = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!joinName.trim() || !pool) return;

    const existingParticipantIndex = participants.findIndex(p =>
      p.name.toLowerCase() === joinName.trim().toLowerCase()
    );

    if (existingParticipantIndex >= 0) {
      // User already exists, just switch to them
      setCurrentParticipant(existingParticipantIndex);
    } else {
      // Add new participant
      try {
        const newParticipant = await addParticipant(joinName.trim());
        if (newParticipant) {
          // Set current participant to the newly added participant
          // The participants array will be updated by the addParticipant function
          // so the new participant will be at index participants.length (before the update)
          setCurrentParticipant(participants.length);
        }
      } catch (err) {
        console.error('Failed to join pool:', err);
        return;
      }
    }

    setHasJoined(true);
    setShowJoinInterface(false);
    setJoinName('');
  };

  const copyShareUrl = async () => {
    setCopyStatus('copying');

    try {
      // Try modern clipboard API first
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(shareUrl);
      } else {
        // Fallback for older browsers or non-secure contexts
        const textArea = document.createElement('textarea');
        textArea.value = shareUrl;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand('copy');
        textArea.remove();
      }

      setCopyStatus('copied');

      // Reset status after 2 seconds
      setTimeout(() => {
        setCopyStatus('idle');
      }, 2000);
    } catch (err) {
      console.error('Failed to copy URL:', err);
      setCopyStatus('error');

      // Reset status after 3 seconds
      setTimeout(() => {
        setCopyStatus('idle');
      }, 3000);
    }
  };

  // Loading state
  if (loading || isCreatingPool) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center px-4">
        <div className="text-center space-y-4">
          <Loader2 className="w-8 h-8 animate-spin mx-auto text-green-600" />
          <p className="text-gray-600">
            {isCreatingPool ? 'Creating your pool...' : 'Loading pool...'}
          </p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center px-4">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold text-gray-900">Pool Not Found</h1>
          <p className="text-gray-600">{error}</p>
          <Button onClick={onBack} variant="outline">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  // Join interface for visitors
  if (showJoinInterface && pool) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center px-4">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center space-y-4">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-50 rounded-full mb-6">
              <UserPlus className="w-8 h-8 text-green-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">
              Join {pool.creator_name}'s Schedule
            </h1>
            <p className="text-gray-600">
              Enter your name to share your availability
            </p>
          </div>

          {participants.length > 0 && (
            <div className="space-y-3">
              <p className="text-sm text-gray-600 text-center">
                Or select your name if you've already joined:
              </p>
              <div className="grid grid-cols-2 gap-2">
                {participants.map((participant, index) => (
                  <button
                    key={participant.id}
                    onClick={() => {
                      setCurrentParticipant(index);
                      setHasJoined(true);
                      setShowJoinInterface(false);
                    }}
                    className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                  >
                    {participant.name}
                  </button>
                ))}
              </div>
            </div>
          )}

          <form onSubmit={handleJoinPool} className="space-y-6">
            <Input
              type="text"
              placeholder="Enter your name"
              value={joinName}
              onChange={(e) => setJoinName(e.target.value)}
              className="text-center text-lg py-6 border-gray-200 focus:border-green-500 focus:ring-green-500"
              maxLength={50}
              required
            />

            <Button
              type="submit"
              className="w-full bg-green-600 hover:bg-green-700 text-white py-6 text-lg font-medium rounded-lg transition-colors"
              disabled={!joinName.trim()}
            >
              Join Pool
            </Button>
          </form>

          <button
            onClick={onBack}
            className="w-full text-gray-500 hover:text-gray-700 transition-colors text-sm"
          >
            ← Create your own pool instead
          </button>
        </div>
      </div>
    );
  }

  // Main coordination interface
  if (!pool) return null;

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <button
            onClick={onBack}
            className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </button>

          <div className="relative">
            <Button
              onClick={copyShareUrl}
              variant="outline"
              disabled={copyStatus === 'copying'}
              className={`inline-flex items-center transition-all duration-300 ease-in-out transform hover:scale-105 active:scale-95 ${
                copyStatus === 'copied'
                  ? 'border-green-500 text-green-700 bg-green-50 shadow-md'
                  : copyStatus === 'error'
                  ? 'border-red-300 text-red-700 bg-red-50 shadow-md'
                  : 'border-green-200 text-green-700 hover:bg-green-50 hover:border-green-300 hover:shadow-sm'
              }`}
            >
              <span className={`transition-all duration-200 ${copyStatus === 'copied' ? 'animate-pulse' : ''}`}>
                {copyStatus === 'copying' && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                {copyStatus === 'copied' && <Check className="w-4 h-4 mr-2 text-green-600" />}
                {copyStatus === 'error' && <AlertCircle className="w-4 h-4 mr-2 text-red-600" />}
                {copyStatus === 'idle' && <Copy className="w-4 h-4 mr-2" />}
              </span>

              <span className="font-medium">
                {copyStatus === 'copying' && 'Copying...'}
                {copyStatus === 'copied' && 'Copied!'}
                {copyStatus === 'error' && 'Try again'}
                {copyStatus === 'idle' && 'Copy Link'}
              </span>
            </Button>

            {/* Helpful message */}
            {copyStatus === 'copied' && (
              <div className="absolute top-full mt-2 right-0 bg-green-100 text-green-800 text-xs px-3 py-1 rounded-md shadow-sm animate-fade-in">
                Share this link with others to join!
              </div>
            )}

            {copyStatus === 'error' && (
              <div className="absolute top-full mt-2 right-0 bg-red-100 text-red-800 text-xs px-3 py-1 rounded-md shadow-sm animate-fade-in">
                Please try copying manually
              </div>
            )}
          </div>
        </div>

        {/* Participants */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <Users className="w-5 h-5 mr-2 text-green-600" />
              Participants ({participants.length})
            </h2>

            {!isAddingParticipant && (
              <Button
                onClick={() => setIsAddingParticipant(true)}
                variant="outline"
                size="sm"
                className="border-green-200 text-green-700 hover:bg-green-50"
              >
                Add Person
              </Button>
            )}
          </div>

          <div className="flex flex-wrap gap-2 mb-4">
            {participants.map((participant, index) => (
              <button
                key={participant.id}
                onClick={() => setCurrentParticipant(index)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  currentParticipant === index
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {participant.name}
                {participant.availability.length > 0 && (
                  <span className="ml-2 text-xs opacity-75">
                    ({participant.availability.length})
                  </span>
                )}
              </button>
            ))}
          </div>

          {isAddingParticipant && (
            <form onSubmit={handleAddParticipant} className="flex gap-2 mb-4">
              <Input
                type="text"
                placeholder="Enter name"
                value={newParticipantName}
                onChange={(e) => setNewParticipantName(e.target.value)}
                className="flex-1"
                autoFocus
                maxLength={50}
              />
              <Button type="submit" disabled={!newParticipantName.trim()}>
                Add
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsAddingParticipant(false);
                  setNewParticipantName('');
                }}
              >
                Cancel
              </Button>
            </form>
          )}
        </div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Calendar */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <Calendar className="w-5 h-5 mr-2 text-green-600" />
              Select Available Dates - {participants[currentParticipant]?.name}
            </h3>

            <CalendarGrid
              selectedDates={participants[currentParticipant]?.availability || []}
              onDateToggle={handleDateToggle}
            />
          </div>

          {/* Availability Display */}
          <AvailabilityDisplay participants={participants} />
        </div>
      </div>
    </div>
  );
};

export default CoordinationPage;
